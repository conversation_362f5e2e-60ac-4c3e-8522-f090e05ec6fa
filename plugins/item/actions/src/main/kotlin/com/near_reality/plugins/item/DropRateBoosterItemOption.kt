package com.near_reality.plugins.item

import com.zenyte.game.item.Item
import com.zenyte.game.model.item.pluginextensions.ItemPlugin
import com.zenyte.game.model.ui.testinterfaces.GameNoticeboardInterface
import com.zenyte.game.util.Colour
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.Dialogue
import com.zenyte.game.world.entity.player.dialogue.dialogue

class DropRateBoosterItemOption: ItemPlugin() {

	override fun handle() {
		bind("Activate") { p: Player, item: Item, _: Int ->
			if (p.variables.dropRateBoosterTick > 0) {
				p.sendMessage("You already have the drop rate boost activated.")
				return@bind
			}

			p.dialogue {
				item(item, "You try to examine this ancient object. It appears to be an archaic invocation of the gods! Would you like to consume its power to obtain: ${Colour.DARK_BLUE.wrap("Drop rate increase by 5% for 1 hour.")}")
				options("This will consume the booster!", Dialogue.DialogueOption("Consume ${item.name}.") {
					if (p.variables.dropRateBoosterTick > 0) {
						p.sendMessage("You already have the drop rate boost activated.")
						return@DialogueOption
					}

					p.variables.dropRateBoosterTick += 6000 // 1 hour in ticks
					p.inventory.deleteItem(item.copy(1))
					GameNoticeboardInterface.updateDropRate(p)
					p.dialogue {
						item(item, "You consume the ancient object. You feel the booster effect flowing through you: ${Colour.DARK_BLUE.wrap("Drop rate increase by 5% for 1 hour.")}")
					}
				}, Dialogue.DialogueOption("Cancel."))
			}
		}
	}

	override fun getItems(): IntArray {
		return intArrayOf(32201)
	}

}